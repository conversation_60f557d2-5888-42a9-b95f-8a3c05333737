from openai import OpenAI
client = OpenAI()

def breakdown_task(title, due_date):
    prompt = f"""
    You are my study assistant. 
    Task: {title}, due: {due_date}.
    Break this into daily study/work sessions with clear steps.
    """
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content

print(breakdown_task("Accounting project", "2025-09-15"))
