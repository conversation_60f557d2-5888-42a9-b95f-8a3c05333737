# Basic requirements for demo interface (no API keys needed)
# For full functionality, uncomment the lines below and set up API keys

# AI and API integrations (requires API keys)
# openai>=1.0.0
# google-api-python-client>=2.0.0
# google-auth>=2.0.0
# google-auth-oauthlib>=1.0.0
# google-auth-httplib2>=0.2.0
# python-telegram-bot>=20.0
# chromadb>=0.4.0

# Scheduling and utilities
schedule>=1.2.0
python-dotenv>=1.0.0
requests>=2.31.0

# Optional: Data analysis and visualization
# pandas>=2.0.0
# numpy>=1.24.0
# matplotlib>=3.7.0
# seaborn>=0.12.0
