# Configuration file for AI Student Assistant
import os
from datetime import datetime, timedelta

# API Keys and Tokens
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', 'your-openai-key-here')
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', 'your-telegram-token-here')
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', 'your-chat-id-here')
GOOGLE_CREDENTIALS_PATH = "credentials.json"

# Database Configuration
CHROMA_DB_PATH = "./chroma_db"
MATERIALS_COLLECTION = "study_materials"
ASSIGNMENTS_COLLECTION = "assignments"

# Scheduling Configuration
DAILY_REMINDER_TIME = "07:00"
WEEKLY_REVIEW_TIME = "09:00"
STUDY_SESSION_DURATION = 45  # minutes
BREAK_DURATION = 15  # minutes

# AI Configuration
DEFAULT_MODEL = "gpt-4o-mini"
MAX_TOKENS = 1000
TEMPERATURE = 0.7

# Study Categories
STUDY_CATEGORIES = [
    "Accounting",
    "Finance", 
    "Mathematics",
    "Computer Science",
    "Business",
    "Economics",
    "Statistics",
    "Other"
]

# Priority Levels
PRIORITY_LEVELS = {
    "HIGH": 1,
    "MEDIUM": 2,
    "LOW": 3
}

# Notification Settings
NOTIFICATION_CHANNELS = {
    "telegram": True,
    "console": True,
    "email": False  # Can be implemented later
}

# Time Management
WORK_HOURS_START = 8  # 8 AM
WORK_HOURS_END = 22   # 10 PM
WEEKEND_WORK_HOURS_START = 10  # 10 AM
WEEKEND_WORK_HOURS_END = 20    # 8 PM
