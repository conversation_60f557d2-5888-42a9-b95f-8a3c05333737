from telegram import Bot
import schedule, time

TELEGRAM_TOKEN = "YOUR_BOT_TOKEN"
CHAT_ID = "YOUR_CHAT_ID"
bot = Bot(token=TELEGRAM_TOKEN)

def send_daily_plan():
    assignments = get_assignments()
    message = "📌 Today's Study Plan:\n"
    for a in assignments:
        steps = breakdown_task(a['title'], a['due_date'])
        message += f"\n🔹 {a['title']} (due {a['due_date']})\n{steps}\n"
    bot.send_message(chat_id=CHAT_ID, text=message)

schedule.every().day.at("07:00").do(send_daily_plan)

while True:
    schedule.run_pending()
    time.sleep(60)
