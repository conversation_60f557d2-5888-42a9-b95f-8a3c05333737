"""
AI-Powered Task Planning and Study Recommendations Module
Uses OpenAI to generate intelligent study plans and recommendations
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from config import OPENAI_API_KEY, DEFAULT_MODEL, MAX_TOKENS, TEMPERATURE

class AITaskPlanner:
    def __init__(self):
        """Initialize OpenAI client"""
        self.client = None
        if OPENAI_AVAILABLE and OPENAI_API_KEY != 'your-openai-key-here':
            try:
                self.client = OpenAI(api_key=OPENAI_API_KEY)
                logging.info("OpenAI client initialized successfully")
            except Exception as e:
                logging.error(f"Failed to initialize OpenAI client: {e}")
        else:
            logging.warning("OpenAI not available. Install openai package and set API key for AI features.")
        
    def create_daily_study_plan(self, assignments: List[Dict]) -> str:
        """Generate a comprehensive daily study plan"""
        if not self.client:
            return self._create_basic_study_plan(assignments)
        
        try:
            if not assignments:
                return "No assignments due soon. Consider reviewing previous materials or getting ahead on upcoming work."
            
            # Prepare assignment data for AI
            assignment_info = []
            for assignment in assignments:
                info = f"- {assignment['title']} (Due: {assignment['due_date']}, Priority: {assignment['priority']}, Subject: {assignment['subject']})"
                assignment_info.append(info)
            
            prompt = f"""
            You are an expert study planner and academic coach. Create a detailed daily study plan for today ({datetime.now().strftime('%A, %B %d, %Y')}).

            Current assignments:
            {chr(10).join(assignment_info)}

            Please provide:
            1. A prioritized study schedule with specific time blocks
            2. Recommended study techniques for each subject
            3. Break times and wellness reminders
            4. Progress milestones to track
            5. Motivational tips

            Format the response clearly with emojis and actionable steps. Keep it concise but comprehensive.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error creating daily study plan: {str(e)}")
            return self._create_basic_study_plan(assignments)
    
    def _create_basic_study_plan(self, assignments: List[Dict]) -> str:
        """Create a basic study plan without AI"""
        if not assignments:
            return "🎉 No urgent assignments! Great time to review previous materials or get ahead."
        
        plan = f"📋 Study Plan for {datetime.now().strftime('%A, %B %d, %Y')}:\n\n"
        
        # Group by priority
        high_priority = [a for a in assignments if a.get('priority') == 'HIGH']
        medium_priority = [a for a in assignments if a.get('priority') == 'MEDIUM']
        low_priority = [a for a in assignments if a.get('priority') == 'LOW']
        
        if high_priority:
            plan += "🚨 URGENT - Focus on these first:\n"
            for assignment in high_priority:
                plan += f"• {assignment['title']} (Due: {assignment['due_date'][:10]})\n"
                plan += f"  📝 Suggested: Spend 2-3 hours today\n"
            plan += "\n"
        
        if medium_priority:
            plan += "⚠️ IMPORTANT - Work on these next:\n"
            for assignment in medium_priority:
                plan += f"• {assignment['title']} (Due: {assignment['due_date'][:10]})\n"
                plan += f"  📝 Suggested: Spend 1-2 hours today\n"
            plan += "\n"
        
        if low_priority:
            plan += "📅 UPCOMING - Plan ahead:\n"
            for assignment in low_priority:
                plan += f"• {assignment['title']} (Due: {assignment['due_date'][:10]})\n"
                plan += f"  📝 Suggested: Start planning and research\n"
            plan += "\n"
        
        plan += "💡 Study Tips:\n"
        plan += "• Take 15-minute breaks every hour\n"
        plan += "• Start with the most challenging tasks\n"
        plan += "• Use the Pomodoro Technique (25 min work, 5 min break)\n"
        plan += "• Stay hydrated and take care of yourself!\n"
        
        return plan
    
    def create_weekly_study_plan(self, assignments: List[Dict]) -> str:
        """Generate a comprehensive weekly study plan"""
        if not self.client:
            return self._create_basic_weekly_plan(assignments)
        
        try:
            if not assignments:
                return "No assignments due this week. Great time to get ahead or review previous materials!"
            
            # Group assignments by subject and priority
            subjects = {}
            for assignment in assignments:
                subject = assignment['subject']
                if subject not in subjects:
                    subjects[subject] = []
                subjects[subject].append(assignment)
            
            prompt = f"""
            You are an expert academic planner. Create a comprehensive weekly study plan for the week of {datetime.now().strftime('%B %d, %Y')}.

            Assignments by subject:
            """
            
            for subject, subject_assignments in subjects.items():
                prompt += f"\n{subject}:\n"
                for assignment in subject_assignments:
                    prompt += f"  - {assignment['title']} (Due: {assignment['due_date']}, Priority: {assignment['priority']})\n"
            
            prompt += """
            Please provide:
            1. Day-by-day study schedule with time allocations
            2. Subject rotation strategy to avoid burnout
            3. Milestone checkpoints throughout the week
            4. Buffer time for unexpected challenges
            5. Weekend review and preparation activities
            6. Work-life balance recommendations

            Format with clear daily sections and actionable tasks.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error creating weekly study plan: {str(e)}")
            return self._create_basic_weekly_plan(assignments)
    
    def _create_basic_weekly_plan(self, assignments: List[Dict]) -> str:
        """Create a basic weekly plan without AI"""
        if not assignments:
            return "No assignments due this week. Great time to get ahead or review previous materials!"
        
        plan = f"📅 Weekly Study Plan - Week of {datetime.now().strftime('%B %d, %Y')}:\n\n"
        
        # Sort assignments by due date
        sorted_assignments = sorted(assignments, key=lambda x: x.get('days_until_due', 999))
        
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        current_day = datetime.now().weekday()
        
        for i, day in enumerate(days):
            plan += f"**{day}:**\n"
            if i < len(sorted_assignments):
                assignment = sorted_assignments[i % len(sorted_assignments)]
                plan += f"• Work on: {assignment['title']}\n"
                plan += f"• Subject: {assignment['subject']}\n"
                plan += f"• Suggested time: 2-3 hours\n"
            else:
                plan += "• Review previous work\n"
                plan += "• Get ahead on upcoming assignments\n"
            plan += "\n"
        
        return plan
    
    def breakdown_assignment(self, title: str, due_date: str, description: str = "") -> str:
        """Break down a complex assignment into manageable tasks"""
        if not self.client:
            return self._create_basic_breakdown(title, due_date, description)
        
        try:
            prompt = f"""
            You are an expert academic coach. Break down this assignment into specific, actionable tasks:

            Assignment: {title}
            Due Date: {due_date}
            Description: {description}

            Please provide:
            1. A step-by-step breakdown of tasks
            2. Estimated time for each task
            3. Recommended order of completion
            4. Key milestones and checkpoints
            5. Resources or materials needed
            6. Potential challenges and solutions

            Format as a clear, numbered list with time estimates.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error breaking down assignment: {str(e)}")
            return self._create_basic_breakdown(title, due_date, description)
    
    def _create_basic_breakdown(self, title: str, due_date: str, description: str = "") -> str:
        """Create a basic assignment breakdown without AI"""
        breakdown = f"📋 Task Breakdown for: {title}\n\n"
        breakdown += f"Due Date: {due_date}\n\n"
        
        breakdown += "Suggested Steps:\n"
        breakdown += "1. Research and gather materials (2-3 hours)\n"
        breakdown += "2. Create an outline or plan (1 hour)\n"
        breakdown += "3. Complete first draft/initial work (3-4 hours)\n"
        breakdown += "4. Review and revise (1-2 hours)\n"
        breakdown += "5. Final review and submission prep (30 minutes)\n\n"
        
        breakdown += "💡 Tips:\n"
        breakdown += "• Start early to avoid last-minute stress\n"
        breakdown += "• Break large tasks into smaller chunks\n"
        breakdown += "• Set mini-deadlines for each step\n"
        breakdown += "• Ask for help if you get stuck\n"
        
        return breakdown
    
    def get_study_recommendations(self, subject: str, current_materials: List[str] = None) -> str:
        """Get AI-powered study recommendations for a subject"""
        if not self.client:
            return self._get_basic_recommendations(subject)
        
        try:
            materials_info = ""
            if current_materials:
                materials_info = f"\nCurrent study materials: {', '.join(current_materials)}"
            
            prompt = f"""
            You are an expert tutor for {subject}. Provide personalized study recommendations.
            {materials_info}

            Please suggest:
            1. Effective study techniques specific to {subject}
            2. Additional resources (books, websites, videos)
            3. Practice exercises or activities
            4. Common pitfalls to avoid
            5. Memory techniques for key concepts
            6. Ways to apply knowledge practically

            Keep recommendations actionable and specific to {subject}.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error getting study recommendations: {str(e)}")
            return self._get_basic_recommendations(subject)
    
    def _get_basic_recommendations(self, subject: str) -> str:
        """Get basic study recommendations without AI"""
        recommendations = f"📚 Study Recommendations for {subject}:\n\n"
        
        if subject.lower() in ['accounting', 'finance']:
            recommendations += "• Practice with real financial statements\n"
            recommendations += "• Use flashcards for key terms and ratios\n"
            recommendations += "• Work through practice problems daily\n"
            recommendations += "• Join study groups for discussion\n"
        elif subject.lower() in ['mathematics', 'statistics']:
            recommendations += "• Practice problems daily\n"
            recommendations += "• Review formulas regularly\n"
            recommendations += "• Explain concepts to others\n"
            recommendations += "• Use visual aids and graphs\n"
        else:
            recommendations += "• Create summary notes\n"
            recommendations += "• Use active recall techniques\n"
            recommendations += "• Practice with past exams\n"
            recommendations += "• Form study groups\n"
        
        recommendations += "\n💡 General Tips:\n"
        recommendations += "• Review material within 24 hours\n"
        recommendations += "• Use spaced repetition\n"
        recommendations += "• Take regular breaks\n"
        recommendations += "• Stay consistent with study schedule\n"
        
        return recommendations
