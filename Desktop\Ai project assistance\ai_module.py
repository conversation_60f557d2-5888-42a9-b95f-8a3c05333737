"""
AI-Powered Task Planning and Study Recommendations Module
Uses OpenAI to generate intelligent study plans and recommendations
"""

from openai import OpenAI
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from config import OPENAI_API_KEY, DEFAULT_MODEL, MAX_TOKENS, TEMPERATURE

class AITaskPlanner:
    def __init__(self):
        """Initialize OpenAI client"""
        self.client = OpenAI(api_key=OPENAI_API_KEY)
        
    def create_daily_study_plan(self, assignments: List[Dict]) -> str:
        """Generate a comprehensive daily study plan"""
        try:
            if not assignments:
                return "No assignments due soon. Consider reviewing previous materials or getting ahead on upcoming work."
            
            # Prepare assignment data for AI
            assignment_info = []
            for assignment in assignments:
                info = f"- {assignment['title']} (Due: {assignment['due_date']}, Priority: {assignment['priority']}, Subject: {assignment['subject']})"
                assignment_info.append(info)
            
            prompt = f"""
            You are an expert study planner and academic coach. Create a detailed daily study plan for today ({datetime.now().strftime('%A, %B %d, %Y')}).

            Current assignments:
            {chr(10).join(assignment_info)}

            Please provide:
            1. A prioritized study schedule with specific time blocks
            2. Recommended study techniques for each subject
            3. Break times and wellness reminders
            4. Progress milestones to track
            5. Motivational tips

            Format the response clearly with emojis and actionable steps. Keep it concise but comprehensive.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error creating daily study plan: {str(e)}")
            return "Unable to generate study plan. Please check your assignments manually."
    
    def create_weekly_study_plan(self, assignments: List[Dict]) -> str:
        """Generate a comprehensive weekly study plan"""
        try:
            if not assignments:
                return "No assignments due this week. Great time to get ahead or review previous materials!"
            
            # Group assignments by subject and priority
            subjects = {}
            for assignment in assignments:
                subject = assignment['subject']
                if subject not in subjects:
                    subjects[subject] = []
                subjects[subject].append(assignment)
            
            prompt = f"""
            You are an expert academic planner. Create a comprehensive weekly study plan for the week of {datetime.now().strftime('%B %d, %Y')}.

            Assignments by subject:
            """
            
            for subject, subject_assignments in subjects.items():
                prompt += f"\n{subject}:\n"
                for assignment in subject_assignments:
                    prompt += f"  - {assignment['title']} (Due: {assignment['due_date']}, Priority: {assignment['priority']})\n"
            
            prompt += """
            Please provide:
            1. Day-by-day study schedule with time allocations
            2. Subject rotation strategy to avoid burnout
            3. Milestone checkpoints throughout the week
            4. Buffer time for unexpected challenges
            5. Weekend review and preparation activities
            6. Work-life balance recommendations

            Format with clear daily sections and actionable tasks.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error creating weekly study plan: {str(e)}")
            return "Unable to generate weekly plan. Please plan manually."
    
    def breakdown_assignment(self, title: str, due_date: str, description: str = "") -> str:
        """Break down a complex assignment into manageable tasks"""
        try:
            prompt = f"""
            You are an expert academic coach. Break down this assignment into specific, actionable tasks:

            Assignment: {title}
            Due Date: {due_date}
            Description: {description}

            Please provide:
            1. A step-by-step breakdown of tasks
            2. Estimated time for each task
            3. Recommended order of completion
            4. Key milestones and checkpoints
            5. Resources or materials needed
            6. Potential challenges and solutions

            Format as a clear, numbered list with time estimates.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error breaking down assignment: {str(e)}")
            return f"Unable to break down assignment. Consider dividing '{title}' into smaller, manageable tasks."
    
    def get_study_recommendations(self, subject: str, current_materials: List[str] = None) -> str:
        """Get AI-powered study recommendations for a subject"""
        try:
            materials_info = ""
            if current_materials:
                materials_info = f"\nCurrent study materials: {', '.join(current_materials)}"
            
            prompt = f"""
            You are an expert tutor for {subject}. Provide personalized study recommendations.
            {materials_info}

            Please suggest:
            1. Effective study techniques specific to {subject}
            2. Additional resources (books, websites, videos)
            3. Practice exercises or activities
            4. Common pitfalls to avoid
            5. Memory techniques for key concepts
            6. Ways to apply knowledge practically

            Keep recommendations actionable and specific to {subject}.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error getting study recommendations: {str(e)}")
            return f"Unable to generate recommendations for {subject}. Consider reviewing your textbook and class notes."
    
    def generate_study_questions(self, topic: str, difficulty: str = "medium") -> str:
        """Generate practice questions for a topic"""
        try:
            prompt = f"""
            You are an expert educator. Generate practice questions for the topic: {topic}

            Difficulty level: {difficulty}

            Please provide:
            1. 5 multiple choice questions with answers
            2. 3 short answer questions
            3. 2 essay/discussion questions
            4. 1 practical application scenario

            Include answer keys and explanations where helpful.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error generating study questions: {str(e)}")
            return f"Unable to generate questions for {topic}. Try creating your own practice questions."
    
    def analyze_study_progress(self, completed_tasks: List[str], remaining_tasks: List[str]) -> str:
        """Analyze study progress and provide feedback"""
        try:
            prompt = f"""
            You are an academic progress coach. Analyze this study progress:

            Completed tasks:
            {chr(10).join(f"- {task}" for task in completed_tasks)}

            Remaining tasks:
            {chr(10).join(f"- {task}" for task in remaining_tasks)}

            Please provide:
            1. Progress assessment and congratulations
            2. Recommendations for remaining tasks
            3. Time management suggestions
            4. Motivation and encouragement
            5. Adjustments to study strategy if needed

            Be encouraging but realistic about the workload.
            """
            
            response = self.client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error analyzing study progress: {str(e)}")
            return "Keep up the good work! Focus on completing your remaining tasks one at a time."
