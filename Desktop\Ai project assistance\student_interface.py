"""
Simple User Interface for AI Student Assistant
Works without API keys for demonstration purposes
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Dict

class SimpleStudentAssistant:
    def __init__(self):
        """Initialize the simple assistant"""
        self.assignments = []
        self.materials = []
        self.tasks = []
        self.load_data()
    
    def load_data(self):
        """Load existing data from files"""
        try:
            if os.path.exists('demo_data.json'):
                with open('demo_data.json', 'r') as f:
                    data = json.load(f)
                    self.assignments = data.get('assignments', [])
                    self.materials = data.get('materials', [])
                    self.tasks = data.get('tasks', [])
        except:
            pass
    
    def save_data(self):
        """Save data to file"""
        try:
            data = {
                'assignments': self.assignments,
                'materials': self.materials,
                'tasks': self.tasks,
                'last_updated': datetime.now().isoformat()
            }
            with open('demo_data.json', 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving data: {e}")
    
    def add_assignment(self, title: str, due_date: str, subject: str, description: str = ""):
        """Add a new assignment"""
        assignment = {
            'id': len(self.assignments) + 1,
            'title': title,
            'due_date': due_date,
            'subject': subject,
            'description': description,
            'status': 'pending',
            'created_date': datetime.now().isoformat()
        }
        self.assignments.append(assignment)
        self.save_data()
        return assignment
    
    def add_material(self, title: str, content: str, subject: str):
        """Add study material"""
        material = {
            'id': len(self.materials) + 1,
            'title': title,
            'content': content,
            'subject': subject,
            'date_added': datetime.now().isoformat()
        }
        self.materials.append(material)
        self.save_data()
        return material
    
    def search_materials(self, query: str) -> List[Dict]:
        """Simple search through materials"""
        query = query.lower()
        results = []
        
        for material in self.materials:
            score = 0
            if query in material['title'].lower():
                score += 3
            if query in material['content'].lower():
                score += 2
            if query in material['subject'].lower():
                score += 1
            
            if score > 0:
                material_copy = material.copy()
                material_copy['relevance_score'] = score
                results.append(material_copy)
        
        return sorted(results, key=lambda x: x['relevance_score'], reverse=True)
    
    def get_upcoming_assignments(self, days_ahead: int = 7) -> List[Dict]:
        """Get assignments due in the next few days"""
        upcoming = []
        current_date = datetime.now()
        
        for assignment in self.assignments:
            if assignment['status'] == 'completed':
                continue
                
            try:
                due_date = datetime.fromisoformat(assignment['due_date'])
                days_until_due = (due_date - current_date).days
                
                if 0 <= days_until_due <= days_ahead:
                    assignment_copy = assignment.copy()
                    assignment_copy['days_until_due'] = days_until_due
                    
                    # Determine priority
                    if days_until_due <= 1:
                        assignment_copy['priority'] = 'HIGH'
                    elif days_until_due <= 3:
                        assignment_copy['priority'] = 'MEDIUM'
                    else:
                        assignment_copy['priority'] = 'LOW'
                    
                    upcoming.append(assignment_copy)
            except:
                continue
        
        return sorted(upcoming, key=lambda x: x['days_until_due'])
    
    def generate_study_plan(self, assignments: List[Dict]) -> str:
        """Generate a simple study plan"""
        if not assignments:
            return "🎉 No urgent assignments! Great time to review previous materials or get ahead."
        
        plan = f"📋 Study Plan for {datetime.now().strftime('%A, %B %d, %Y')}:\n\n"
        
        # Group by priority
        high_priority = [a for a in assignments if a.get('priority') == 'HIGH']
        medium_priority = [a for a in assignments if a.get('priority') == 'MEDIUM']
        low_priority = [a for a in assignments if a.get('priority') == 'LOW']
        
        if high_priority:
            plan += "🚨 URGENT - Focus on these first:\n"
            for assignment in high_priority:
                plan += f"• {assignment['title']} (Due: {assignment['due_date'][:10]})\n"
                plan += f"  📝 Suggested: Spend 2-3 hours today\n"
            plan += "\n"
        
        if medium_priority:
            plan += "⚠️ IMPORTANT - Work on these next:\n"
            for assignment in medium_priority:
                plan += f"• {assignment['title']} (Due: {assignment['due_date'][:10]})\n"
                plan += f"  📝 Suggested: Spend 1-2 hours today\n"
            plan += "\n"
        
        if low_priority:
            plan += "📅 UPCOMING - Plan ahead:\n"
            for assignment in low_priority:
                plan += f"• {assignment['title']} (Due: {assignment['due_date'][:10]})\n"
                plan += f"  📝 Suggested: Start planning and research\n"
            plan += "\n"
        
        plan += "💡 Study Tips:\n"
        plan += "• Take 15-minute breaks every hour\n"
        plan += "• Start with the most challenging tasks\n"
        plan += "• Use the Pomodoro Technique (25 min work, 5 min break)\n"
        plan += "• Stay hydrated and take care of yourself!\n"
        
        return plan
    
    def mark_assignment_completed(self, assignment_id: int):
        """Mark an assignment as completed"""
        for assignment in self.assignments:
            if assignment['id'] == assignment_id:
                assignment['status'] = 'completed'
                assignment['completed_date'] = datetime.now().isoformat()
                self.save_data()
                return True
        return False
    
    def get_progress_summary(self) -> str:
        """Get a summary of progress"""
        total_assignments = len(self.assignments)
        completed = len([a for a in self.assignments if a['status'] == 'completed'])
        pending = total_assignments - completed
        
        summary = f"📊 Progress Summary:\n\n"
        summary += f"✅ Completed Assignments: {completed}\n"
        summary += f"📋 Pending Assignments: {pending}\n"
        summary += f"📚 Study Materials: {len(self.materials)}\n"
        
        if total_assignments > 0:
            completion_rate = (completed / total_assignments) * 100
            summary += f"📈 Completion Rate: {completion_rate:.1f}%\n\n"
            
            if completion_rate >= 80:
                summary += "🌟 Excellent work! Keep it up!"
            elif completion_rate >= 60:
                summary += "👍 Good progress! You're doing well!"
            elif completion_rate >= 40:
                summary += "💪 Keep pushing! You're making progress!"
            else:
                summary += "🚀 Time to focus! You can do this!"
        
        return summary

def main_menu():
    """Display the main menu"""
    print("\n" + "="*50)
    print("🤖 AI Student Assistant - Demo Interface")
    print("="*50)
    print("1. Add Assignment")
    print("2. Add Study Material")
    print("3. View Upcoming Assignments")
    print("4. Generate Study Plan")
    print("5. Search Study Materials")
    print("6. Mark Assignment Complete")
    print("7. View Progress Summary")
    print("8. View All Data")
    print("9. Exit")
    print("="*50)

def main():
    """Main application loop"""
    assistant = SimpleStudentAssistant()
    
    print("🎉 Welcome to your AI Student Assistant!")
    print("This demo version works without API keys.")
    
    while True:
        main_menu()
        choice = input("\nEnter your choice (1-9): ").strip()
        
        if choice == '1':
            print("\n📝 Add New Assignment")
            title = input("Assignment title: ")
            due_date = input("Due date (YYYY-MM-DD): ")
            subject = input("Subject: ")
            description = input("Description (optional): ")
            
            try:
                # Validate date format
                datetime.fromisoformat(due_date)
                assignment = assistant.add_assignment(title, due_date, subject, description)
                print(f"✅ Added assignment: {assignment['title']}")
            except ValueError:
                print("❌ Invalid date format. Please use YYYY-MM-DD")
        
        elif choice == '2':
            print("\n📚 Add Study Material")
            title = input("Material title: ")
            subject = input("Subject: ")
            content = input("Content/Notes: ")
            
            material = assistant.add_material(title, content, subject)
            print(f"✅ Added material: {material['title']}")
        
        elif choice == '3':
            print("\n📅 Upcoming Assignments")
            upcoming = assistant.get_upcoming_assignments()
            
            if not upcoming:
                print("🎉 No upcoming assignments in the next 7 days!")
            else:
                for assignment in upcoming:
                    priority_emoji = {"HIGH": "🚨", "MEDIUM": "⚠️", "LOW": "📅"}
                    emoji = priority_emoji.get(assignment['priority'], "📋")
                    print(f"{emoji} {assignment['title']}")
                    print(f"   Due: {assignment['due_date'][:10]} ({assignment['days_until_due']} days)")
                    print(f"   Subject: {assignment['subject']}")
                    if assignment.get('description'):
                        print(f"   Description: {assignment['description']}")
                    print()
        
        elif choice == '4':
            print("\n📋 Generating Study Plan...")
            upcoming = assistant.get_upcoming_assignments()
            study_plan = assistant.generate_study_plan(upcoming)
            print(study_plan)
        
        elif choice == '5':
            print("\n🔍 Search Study Materials")
            query = input("Enter search term: ")
            results = assistant.search_materials(query)
            
            if not results:
                print(f"No materials found for '{query}'")
            else:
                print(f"\nFound {len(results)} materials:")
                for i, material in enumerate(results[:5], 1):
                    print(f"{i}. {material['title']} (Subject: {material['subject']})")
                    print(f"   Content preview: {material['content'][:100]}...")
                    print()
        
        elif choice == '6':
            print("\n✅ Mark Assignment Complete")
            pending_assignments = [a for a in assistant.assignments if a['status'] != 'completed']
            
            if not pending_assignments:
                print("No pending assignments to complete!")
            else:
                print("Pending assignments:")
                for i, assignment in enumerate(pending_assignments, 1):
                    print(f"{i}. {assignment['title']} (Due: {assignment['due_date'][:10]})")
                
                try:
                    choice_num = int(input("\nEnter assignment number to mark complete: "))
                    if 1 <= choice_num <= len(pending_assignments):
                        assignment = pending_assignments[choice_num - 1]
                        assistant.mark_assignment_completed(assignment['id'])
                        print(f"✅ Marked '{assignment['title']}' as completed!")
                    else:
                        print("Invalid assignment number.")
                except ValueError:
                    print("Please enter a valid number.")
        
        elif choice == '7':
            print("\n📊 Progress Summary")
            summary = assistant.get_progress_summary()
            print(summary)
        
        elif choice == '8':
            print("\n📋 All Your Data")
            print(f"Assignments: {len(assistant.assignments)}")
            for assignment in assistant.assignments:
                status_emoji = "✅" if assignment['status'] == 'completed' else "📋"
                print(f"  {status_emoji} {assignment['title']} - {assignment['subject']}")
            
            print(f"\nStudy Materials: {len(assistant.materials)}")
            for material in assistant.materials:
                print(f"  📚 {material['title']} - {material['subject']}")
        
        elif choice == '9':
            print("\n👋 Thank you for using AI Student Assistant!")
            print("Your data has been saved to 'demo_data.json'")
            break
        
        else:
            print("❌ Invalid choice. Please enter 1-9.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
