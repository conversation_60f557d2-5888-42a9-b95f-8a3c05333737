"""
Study Materials Management Module
Uses ChromaDB for intelligent material storage and retrieval
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Optional
import logging

try:
    import chromadb
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

from config import CHROMA_DB_PATH, MATERIALS_COLLECTION, STUDY_CATEGORIES

class MaterialsManager:
    def __init__(self):
        """Initialize ChromaDB client and collection"""
        self.client = None
        self.collection = None
        
        if CHROMADB_AVAILABLE:
            try:
                self.client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
                self.collection = self._get_or_create_collection()
                logging.info("ChromaDB initialized successfully")
            except Exception as e:
                logging.error(f"Failed to initialize ChromaDB: {e}")
                self._init_fallback_storage()
        else:
            logging.warning("ChromaDB not available. Using fallback file storage.")
            self._init_fallback_storage()
    
    def _init_fallback_storage(self):
        """Initialize fallback file-based storage"""
        self.materials_file = "materials_storage.json"
        self.materials_data = self._load_materials_from_file()
    
    def _load_materials_from_file(self) -> List[Dict]:
        """Load materials from JSON file"""
        try:
            if os.path.exists(self.materials_file):
                with open(self.materials_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logging.error(f"Error loading materials from file: {e}")
            return []
    
    def _save_materials_to_file(self):
        """Save materials to JSON file"""
        try:
            with open(self.materials_file, 'w', encoding='utf-8') as f:
                json.dump(self.materials_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Error saving materials to file: {e}")
        
    def _get_or_create_collection(self):
        """Get existing collection or create new one"""
        if not self.client:
            return None
            
        try:
            collection = self.client.get_collection(name=MATERIALS_COLLECTION)
            logging.info(f"Connected to existing collection: {MATERIALS_COLLECTION}")
        except:
            collection = self.client.create_collection(name=MATERIALS_COLLECTION)
            logging.info(f"Created new collection: {MATERIALS_COLLECTION}")
        return collection
    
    def add_material(self, title: str, content: str, category: str = "Other", 
                    source: str = "", tags: List[str] = None) -> bool:
        """Add new study material to the knowledge base"""
        try:
            # Generate unique ID
            material_id = f"{category}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(title)}"
            
            # Prepare metadata
            metadata = {
                "title": title,
                "category": category,
                "source": source,
                "date_added": datetime.now().isoformat(),
                "tags": tags or []
            }
            
            if self.collection:
                # Add to ChromaDB collection
                self.collection.add(
                    documents=[content],
                    metadatas=[metadata],
                    ids=[material_id]
                )
            else:
                # Add to fallback storage
                material = {
                    "id": material_id,
                    "title": title,
                    "content": content,
                    "category": category,
                    "source": source,
                    "date_added": datetime.now().isoformat(),
                    "tags": tags or []
                }
                self.materials_data.append(material)
                self._save_materials_to_file()
            
            logging.info(f"Added material: {title}")
            return True
            
        except Exception as e:
            logging.error(f"Error adding material: {str(e)}")
            return False
    
    def search_materials(self, query: str, n_results: int = 5, 
                        category_filter: str = None) -> List[Dict]:
        """Search for relevant study materials"""
        if self.collection:
            return self._search_with_chromadb(query, n_results, category_filter)
        else:
            return self._search_with_fallback(query, n_results, category_filter)
    
    def _search_with_chromadb(self, query: str, n_results: int, category_filter: str) -> List[Dict]:
        """Search using ChromaDB"""
        try:
            # Prepare where clause for filtering
            where_clause = {}
            if category_filter:
                where_clause["category"] = category_filter
            
            # Perform search
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where_clause if where_clause else None
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if results['metadatas'] else {}
                    distance = results['distances'][0][i] if results['distances'] else 0
                    
                    formatted_results.append({
                        'title': metadata.get('title', 'Untitled'),
                        'content': doc,
                        'category': metadata.get('category', 'Other'),
                        'source': metadata.get('source', ''),
                        'date_added': metadata.get('date_added', ''),
                        'tags': metadata.get('tags', []),
                        'relevance_score': 1 - distance  # Convert distance to relevance
                    })
            
            logging.info(f"Found {len(formatted_results)} materials for query: {query}")
            return formatted_results
            
        except Exception as e:
            logging.error(f"Error searching materials with ChromaDB: {str(e)}")
            return []
    
    def _search_with_fallback(self, query: str, n_results: int, category_filter: str) -> List[Dict]:
        """Search using fallback file storage"""
        try:
            query_lower = query.lower()
            results = []
            
            for material in self.materials_data:
                # Apply category filter
                if category_filter and material.get('category') != category_filter:
                    continue
                
                # Calculate relevance score
                score = 0
                title = material.get('title', '').lower()
                content = material.get('content', '').lower()
                category = material.get('category', '').lower()
                
                if query_lower in title:
                    score += 3
                if query_lower in content:
                    score += 2
                if query_lower in category:
                    score += 1
                
                # Check tags
                tags = material.get('tags', [])
                for tag in tags:
                    if query_lower in tag.lower():
                        score += 1
                
                if score > 0:
                    result = material.copy()
                    result['relevance_score'] = score
                    results.append(result)
            
            # Sort by relevance and limit results
            results.sort(key=lambda x: x['relevance_score'], reverse=True)
            return results[:n_results]
            
        except Exception as e:
            logging.error(f"Error searching materials with fallback: {str(e)}")
            return []
    
    def find_relevant_materials(self, assignment_title: str, n_results: int = 3) -> List[Dict]:
        """Find materials relevant to a specific assignment"""
        return self.search_materials(assignment_title, n_results)
    
    def get_materials_by_category(self, category: str) -> List[Dict]:
        """Get all materials in a specific category"""
        if self.collection:
            return self._get_by_category_chromadb(category)
        else:
            return self._get_by_category_fallback(category)
    
    def _get_by_category_chromadb(self, category: str) -> List[Dict]:
        """Get materials by category using ChromaDB"""
        try:
            # Get all documents in the collection
            all_results = self.collection.get(
                where={"category": category}
            )
            
            formatted_results = []
            if all_results['documents']:
                for i, doc in enumerate(all_results['documents']):
                    metadata = all_results['metadatas'][i] if all_results['metadatas'] else {}
                    
                    formatted_results.append({
                        'title': metadata.get('title', 'Untitled'),
                        'content': doc,
                        'category': metadata.get('category', 'Other'),
                        'source': metadata.get('source', ''),
                        'date_added': metadata.get('date_added', ''),
                        'tags': metadata.get('tags', [])
                    })
            
            return formatted_results
            
        except Exception as e:
            logging.error(f"Error getting materials by category with ChromaDB: {str(e)}")
            return []
    
    def _get_by_category_fallback(self, category: str) -> List[Dict]:
        """Get materials by category using fallback storage"""
        return [material for material in self.materials_data 
                if material.get('category') == category]
    
    def add_material_from_file(self, file_path: str, category: str = "Other", 
                              tags: List[str] = None) -> bool:
        """Add study material from a file"""
        try:
            if not os.path.exists(file_path):
                logging.error(f"File not found: {file_path}")
                return False
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Extract title from filename
            title = os.path.splitext(os.path.basename(file_path))[0]
            
            return self.add_material(
                title=title,
                content=content,
                category=category,
                source=file_path,
                tags=tags
            )
            
        except Exception as e:
            logging.error(f"Error adding material from file: {str(e)}")
            return False
    
    def get_material_statistics(self) -> Dict:
        """Get statistics about the material collection"""
        try:
            if self.collection:
                # Get all materials from ChromaDB
                all_materials = self.collection.get()
                metadatas = all_materials.get('metadatas', [])
            else:
                # Get from fallback storage
                metadatas = self.materials_data
            
            if not metadatas:
                return {"total_materials": 0, "categories": {}}
            
            # Count by category
            category_counts = {}
            for metadata in metadatas:
                category = metadata.get('category', 'Other')
                category_counts[category] = category_counts.get(category, 0) + 1
            
            return {
                "total_materials": len(metadatas),
                "categories": category_counts,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error getting material statistics: {str(e)}")
            return {"total_materials": 0, "categories": {}}
    
    def export_materials(self, export_path: str) -> bool:
        """Export all materials to a JSON file"""
        try:
            if self.collection:
                all_materials = self.collection.get()
                export_data = []
                
                if all_materials['documents']:
                    for i, doc in enumerate(all_materials['documents']):
                        metadata = all_materials['metadatas'][i] if all_materials['metadatas'] else {}
                        material_id = all_materials['ids'][i] if all_materials['ids'] else f"material_{i}"
                        
                        export_data.append({
                            'id': material_id,
                            'content': doc,
                            'metadata': metadata
                        })
            else:
                export_data = self.materials_data
            
            with open(export_path, 'w', encoding='utf-8') as file:
                json.dump(export_data, file, indent=2, ensure_ascii=False)
            
            logging.info(f"Exported {len(export_data)} materials to {export_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error exporting materials: {str(e)}")
            return False
