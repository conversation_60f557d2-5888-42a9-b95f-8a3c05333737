"""
Study Materials Management Module
Uses ChromaDB for intelligent material storage and retrieval
"""

import chromadb
import os
import json
from datetime import datetime
from typing import List, Dict, Optional
import logging
from config import CHROMA_DB_PATH, MATERIALS_COLLECTION, STUDY_CATEGORIES

class MaterialsManager:
    def __init__(self):
        """Initialize ChromaDB client and collection"""
        self.client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
        self.collection = self._get_or_create_collection()
        
    def _get_or_create_collection(self):
        """Get existing collection or create new one"""
        try:
            collection = self.client.get_collection(name=MATERIALS_COLLECTION)
            logging.info(f"Connected to existing collection: {MATERIALS_COLLECTION}")
        except:
            collection = self.client.create_collection(name=MATERIALS_COLLECTION)
            logging.info(f"Created new collection: {MATERIALS_COLLECTION}")
        return collection
    
    def add_material(self, title: str, content: str, category: str = "Other", 
                    source: str = "", tags: List[str] = None) -> bool:
        """Add new study material to the knowledge base"""
        try:
            # Generate unique ID
            material_id = f"{category}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(title)}"
            
            # Prepare metadata
            metadata = {
                "title": title,
                "category": category,
                "source": source,
                "date_added": datetime.now().isoformat(),
                "tags": tags or []
            }
            
            # Add to collection
            self.collection.add(
                documents=[content],
                metadatas=[metadata],
                ids=[material_id]
            )
            
            logging.info(f"Added material: {title}")
            return True
            
        except Exception as e:
            logging.error(f"Error adding material: {str(e)}")
            return False
    
    def search_materials(self, query: str, n_results: int = 5, 
                        category_filter: str = None) -> List[Dict]:
        """Search for relevant study materials"""
        try:
            # Prepare where clause for filtering
            where_clause = {}
            if category_filter:
                where_clause["category"] = category_filter
            
            # Perform search
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where_clause if where_clause else None
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if results['metadatas'] else {}
                    distance = results['distances'][0][i] if results['distances'] else 0
                    
                    formatted_results.append({
                        'title': metadata.get('title', 'Untitled'),
                        'content': doc,
                        'category': metadata.get('category', 'Other'),
                        'source': metadata.get('source', ''),
                        'date_added': metadata.get('date_added', ''),
                        'tags': metadata.get('tags', []),
                        'relevance_score': 1 - distance  # Convert distance to relevance
                    })
            
            logging.info(f"Found {len(formatted_results)} materials for query: {query}")
            return formatted_results
            
        except Exception as e:
            logging.error(f"Error searching materials: {str(e)}")
            return []
    
    def find_relevant_materials(self, assignment_title: str, n_results: int = 3) -> List[Dict]:
        """Find materials relevant to a specific assignment"""
        return self.search_materials(assignment_title, n_results)
    
    def get_materials_by_category(self, category: str) -> List[Dict]:
        """Get all materials in a specific category"""
        try:
            # Get all documents in the collection
            all_results = self.collection.get(
                where={"category": category}
            )
            
            formatted_results = []
            if all_results['documents']:
                for i, doc in enumerate(all_results['documents']):
                    metadata = all_results['metadatas'][i] if all_results['metadatas'] else {}
                    
                    formatted_results.append({
                        'title': metadata.get('title', 'Untitled'),
                        'content': doc,
                        'category': metadata.get('category', 'Other'),
                        'source': metadata.get('source', ''),
                        'date_added': metadata.get('date_added', ''),
                        'tags': metadata.get('tags', [])
                    })
            
            return formatted_results
            
        except Exception as e:
            logging.error(f"Error getting materials by category: {str(e)}")
            return []
    
    def add_material_from_file(self, file_path: str, category: str = "Other", 
                              tags: List[str] = None) -> bool:
        """Add study material from a file"""
        try:
            if not os.path.exists(file_path):
                logging.error(f"File not found: {file_path}")
                return False
            
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Extract title from filename
            title = os.path.splitext(os.path.basename(file_path))[0]
            
            return self.add_material(
                title=title,
                content=content,
                category=category,
                source=file_path,
                tags=tags
            )
            
        except Exception as e:
            logging.error(f"Error adding material from file: {str(e)}")
            return False
    
    def update_material_tags(self, material_id: str, new_tags: List[str]) -> bool:
        """Update tags for a specific material"""
        try:
            # Get current material
            result = self.collection.get(ids=[material_id])
            if not result['documents']:
                logging.error(f"Material not found: {material_id}")
                return False
            
            # Update metadata
            current_metadata = result['metadatas'][0]
            current_metadata['tags'] = new_tags
            
            # Update in collection
            self.collection.update(
                ids=[material_id],
                metadatas=[current_metadata]
            )
            
            logging.info(f"Updated tags for material: {material_id}")
            return True
            
        except Exception as e:
            logging.error(f"Error updating material tags: {str(e)}")
            return False
    
    def delete_material(self, material_id: str) -> bool:
        """Delete a material from the knowledge base"""
        try:
            self.collection.delete(ids=[material_id])
            logging.info(f"Deleted material: {material_id}")
            return True
            
        except Exception as e:
            logging.error(f"Error deleting material: {str(e)}")
            return False
    
    def get_material_statistics(self) -> Dict:
        """Get statistics about the material collection"""
        try:
            # Get all materials
            all_materials = self.collection.get()
            
            if not all_materials['metadatas']:
                return {"total_materials": 0, "categories": {}}
            
            # Count by category
            category_counts = {}
            for metadata in all_materials['metadatas']:
                category = metadata.get('category', 'Other')
                category_counts[category] = category_counts.get(category, 0) + 1
            
            return {
                "total_materials": len(all_materials['documents']),
                "categories": category_counts,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logging.error(f"Error getting material statistics: {str(e)}")
            return {"total_materials": 0, "categories": {}}
    
    def export_materials(self, export_path: str) -> bool:
        """Export all materials to a JSON file"""
        try:
            all_materials = self.collection.get()
            
            export_data = []
            if all_materials['documents']:
                for i, doc in enumerate(all_materials['documents']):
                    metadata = all_materials['metadatas'][i] if all_materials['metadatas'] else {}
                    material_id = all_materials['ids'][i] if all_materials['ids'] else f"material_{i}"
                    
                    export_data.append({
                        'id': material_id,
                        'content': doc,
                        'metadata': metadata
                    })
            
            with open(export_path, 'w', encoding='utf-8') as file:
                json.dump(export_data, file, indent=2, ensure_ascii=False)
            
            logging.info(f"Exported {len(export_data)} materials to {export_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error exporting materials: {str(e)}")
            return False
    
    def import_materials(self, import_path: str) -> bool:
        """Import materials from a JSON file"""
        try:
            if not os.path.exists(import_path):
                logging.error(f"Import file not found: {import_path}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as file:
                import_data = json.load(file)
            
            success_count = 0
            for material in import_data:
                metadata = material.get('metadata', {})
                if self.add_material(
                    title=metadata.get('title', 'Imported Material'),
                    content=material.get('content', ''),
                    category=metadata.get('category', 'Other'),
                    source=metadata.get('source', import_path),
                    tags=metadata.get('tags', [])
                ):
                    success_count += 1
            
            logging.info(f"Imported {success_count}/{len(import_data)} materials from {import_path}")
            return success_count > 0
            
        except Exception as e:
            logging.error(f"Error importing materials: {str(e)}")
            return False
