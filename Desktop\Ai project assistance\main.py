import schedule, time
from calendar_module import get_assignments
from planner_module import breakdown_task
from reminder_module import send_message

def daily_plan():
    assignments = get_assignments()
    if not assignments:
        send_message("✅ No upcoming assignments today.")
        return
    
    message = "📅 Your Study Plan Today:\n"
    for a in assignments:
        steps = breakdown_task(a['title'], a['due_date'])
        message += f"\n🔹 {a['title']} (due {a['due_date']})\n{steps}\n"
    
    send_message(message)

# Schedule daily reminder at 7AM
schedule.every().day.at("07:00").do(daily_plan)

while True:
    schedule.run_pending()
    time.sleep(60)
