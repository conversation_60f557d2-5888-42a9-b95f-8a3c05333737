"""
Enhanced AI Student Assistant
Combines all functionality into a comprehensive system
"""

import os
import json
import schedule
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

# Import our modules
from config import *
from calendar_module import CalendarManager
from ai_module import AITaskPlanner
from materials_module import MaterialsManager
from notification_module import NotificationManager
from deadline_module import DeadlineManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('student_assistant.log'),
        logging.StreamHandler()
    ]
)

class StudentAssistant:
    def __init__(self):
        """Initialize the AI Student Assistant"""
        self.calendar_manager = CalendarManager()
        self.ai_planner = AITaskPlanner()
        self.materials_manager = MaterialsManager()
        self.notification_manager = NotificationManager()
        self.deadline_manager = DeadlineManager()
        
        logging.info("AI Student Assistant initialized successfully")
    
    def daily_planning_routine(self):
        """Main daily planning routine"""
        try:
            logging.info("Starting daily planning routine")
            
            # Get upcoming assignments
            assignments = self.calendar_manager.get_upcoming_assignments()
            
            if not assignments:
                self.notification_manager.send_message("✅ No upcoming assignments today!")
                return
            
            # Generate AI-powered study plan
            study_plan = self.ai_planner.create_daily_study_plan(assignments)
            
            # Check for urgent deadlines
            urgent_tasks = self.deadline_manager.check_urgent_deadlines(assignments)
            
            # Find relevant study materials
            relevant_materials = []
            for assignment in assignments:
                materials = self.materials_manager.find_relevant_materials(assignment['title'])
                relevant_materials.extend(materials)
            
            # Create comprehensive message
            message = self._format_daily_message(study_plan, urgent_tasks, relevant_materials)
            
            # Send notifications
            self.notification_manager.send_message(message)
            
            logging.info("Daily planning routine completed successfully")
            
        except Exception as e:
            logging.error(f"Error in daily planning routine: {str(e)}")
            self.notification_manager.send_message(f"❌ Error in daily planning: {str(e)}")
    
    def weekly_review_routine(self):
        """Weekly review and planning"""
        try:
            logging.info("Starting weekly review routine")
            
            # Get all assignments for the week
            assignments = self.calendar_manager.get_weekly_assignments()
            
            # Generate weekly study plan
            weekly_plan = self.ai_planner.create_weekly_study_plan(assignments)
            
            # Check progress on previous week's goals
            progress_report = self.deadline_manager.generate_progress_report()
            
            message = f"📊 Weekly Review & Planning:\n\n{progress_report}\n\n📅 This Week's Plan:\n{weekly_plan}"
            
            self.notification_manager.send_message(message)
            
            logging.info("Weekly review routine completed successfully")
            
        except Exception as e:
            logging.error(f"Error in weekly review routine: {str(e)}")
    
    def add_study_material(self, title: str, content: str, category: str = "Other"):
        """Add new study material to the knowledge base"""
        try:
            self.materials_manager.add_material(title, content, category)
            self.notification_manager.send_message(f"📚 Added new study material: {title}")
            logging.info(f"Added study material: {title}")
        except Exception as e:
            logging.error(f"Error adding study material: {str(e)}")
    
    def search_materials(self, query: str) -> List[Dict]:
        """Search for relevant study materials"""
        try:
            results = self.materials_manager.search_materials(query)
            return results
        except Exception as e:
            logging.error(f"Error searching materials: {str(e)}")
            return []
    
    def get_study_recommendations(self, subject: str) -> str:
        """Get AI-powered study recommendations"""
        try:
            recommendations = self.ai_planner.get_study_recommendations(subject)
            return recommendations
        except Exception as e:
            logging.error(f"Error getting study recommendations: {str(e)}")
            return "Unable to generate recommendations at this time."
    
    def _format_daily_message(self, study_plan: str, urgent_tasks: List, materials: List) -> str:
        """Format the daily message with all information"""
        message = f"🌅 Good Morning! Here's your study plan for {datetime.now().strftime('%B %d, %Y')}:\n\n"
        
        # Add urgent tasks if any
        if urgent_tasks:
            message += "🚨 URGENT DEADLINES:\n"
            for task in urgent_tasks:
                message += f"• {task['title']} - Due: {task['due_date']}\n"
            message += "\n"
        
        # Add study plan
        message += f"📋 Today's Study Plan:\n{study_plan}\n\n"
        
        # Add relevant materials
        if materials:
            message += "📚 Relevant Study Materials:\n"
            for material in materials[:3]:  # Limit to top 3
                message += f"• {material['title']}\n"
            message += "\n"
        
        message += "💪 You've got this! Stay focused and take breaks when needed."
        
        return message
    
    def start_scheduler(self):
        """Start the scheduling system"""
        logging.info("Starting scheduler...")
        
        # Schedule daily planning
        schedule.every().day.at(DAILY_REMINDER_TIME).do(self.daily_planning_routine)
        
        # Schedule weekly review (every Sunday)
        schedule.every().sunday.at(WEEKLY_REVIEW_TIME).do(self.weekly_review_routine)
        
        # Schedule deadline checks (every 2 hours during work hours)
        for hour in range(WORK_HOURS_START, WORK_HOURS_END, 2):
            schedule.every().day.at(f"{hour:02d}:00").do(self.deadline_manager.check_urgent_deadlines)
        
        logging.info("Scheduler started successfully")
        
        # Main loop
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

if __name__ == "__main__":
    assistant = StudentAssistant()
    assistant.start_scheduler()
