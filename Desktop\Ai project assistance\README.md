# 🤖 AI Student Assistant - Demo Version

A comprehensive AI-powered student assistant that helps you manage assignments, organize study materials, and create intelligent study plans.

## 🚀 Quick Start (No API Keys Required)

### 1. Install Basic Dependencies
```bash
pip install schedule python-dotenv requests
```

### 2. Run the Demo Interface
```bash
python student_interface.py
```

That's it! The demo works without any API keys or external services.

## 📋 Demo Features

### ✅ What You Can Do Right Now:
- **Add Assignments** - Track your homework, projects, and exams
- **Add Study Materials** - Store notes, summaries, and study content
- **View Upcoming Deadlines** - See what's due soon with priority levels
- **Generate Study Plans** - Get organized daily study recommendations
- **Search Materials** - Find relevant study content quickly
- **Track Progress** - Mark assignments complete and see your progress
- **Data Persistence** - Your data is saved automatically

### 🎯 How It Works:
1. **Smart Prioritization** - Assignments are automatically prioritized based on due dates
2. **Intelligent Search** - Materials are ranked by relevance to your search terms
3. **Study Planning** - Get personalized daily plans based on your deadlines
4. **Progress Tracking** - Monitor completion rates and productivity

## 📱 Demo Interface Menu

```
🤖 AI Student Assistant - Demo Interface
==================================================
1. Add Assignment
2. Add Study Material  
3. View Upcoming Assignments
4. Generate Study Plan
5. Search Study Materials
6. Mark Assignment Complete
7. View Progress Summary
8. View All Data
9. Exit
```

## 💡 Example Usage

### Adding an Assignment:
```
Assignment title: Financial Statement Analysis Project
Due date: 2024-12-15
Subject: Accounting
Description: Analyze Apple Inc.'s financial statements
```

### Adding Study Material:
```
Material title: Balance Sheet Fundamentals
Subject: Accounting
Content: A balance sheet shows assets, liabilities, and equity...
```

### Generated Study Plan Example:
```
📋 Study Plan for Monday, December 02, 2024:

🚨 URGENT - Focus on these first:
• Financial Statement Analysis Project (Due: 2024-12-15)
  📝 Suggested: Spend 2-3 hours today

💡 Study Tips:
• Take 15-minute breaks every hour
• Start with the most challenging tasks
• Use the Pomodoro Technique (25 min work, 5 min break)
• Stay hydrated and take care of yourself!
```

## 🔧 Full Version Setup (Optional)

For the complete AI-powered experience with OpenAI integration, Google Calendar sync, and Telegram notifications:

### 1. Install Full Dependencies
```bash
pip install -r requirements.txt
# (uncomment the API-related lines in requirements.txt first)
```

### 2. Set Up API Keys
Create a `.env` file:
```
OPENAI_API_KEY=your_openai_key_here
TELEGRAM_BOT_TOKEN=your_telegram_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

### 3. Add Google Calendar Credentials
- Download `credentials.json` from Google Cloud Console
- Place it in the project directory

### 4. Run Full Version
```bash
python ai_assistant.py
```

## 📁 File Structure

```
ai-student-assistant/
├── student_interface.py    # 🎯 Demo interface (START HERE)
├── ai_assistant.py        # Full AI-powered system
├── config.py             # Configuration settings
├── calendar_module.py    # Google Calendar integration
├── ai_module.py         # OpenAI integration
├── materials_module.py  # ChromaDB material management
├── notification_module.py # Multi-channel notifications
├── deadline_module.py   # Deadline tracking
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## 🎓 Perfect for Students Who Want To:

- **Stay Organized** - Never miss a deadline again
- **Study Smarter** - Get AI-generated study plans
- **Find Information Fast** - Search through all your materials instantly
- **Track Progress** - See your productivity and completion rates
- **Reduce Stress** - Automated reminders and planning

## 🌟 Demo vs Full Version

| Feature | Demo Version | Full Version |
|---------|-------------|--------------|
| Assignment Tracking | ✅ | ✅ |
| Study Materials | ✅ | ✅ |
| Study Plans | ✅ Basic | ✅ AI-Powered |
| Progress Tracking | ✅ | ✅ |
| Calendar Integration | ❌ | ✅ Google Calendar |
| AI Recommendations | ❌ | ✅ OpenAI GPT |
| Smart Notifications | ❌ | ✅ Telegram + More |
| Material Search | ✅ Basic | ✅ AI-Powered |

## 🚀 Getting Started Tips

1. **Start with the demo** - Run `python student_interface.py`
2. **Add a few assignments** with realistic due dates
3. **Add some study materials** related to your subjects
4. **Generate a study plan** to see the magic happen
5. **Explore all menu options** to see what's possible

## 📞 Need Help?

- Check `demo_data.json` to see your saved data
- All demo data is stored locally - no cloud required
- The interface is self-explanatory with clear prompts
- Your data persists between sessions automatically

---

**Ready to boost your productivity? Run `python student_interface.py` and start organizing your student life! 🎓**
