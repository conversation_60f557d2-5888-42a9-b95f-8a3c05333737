"""
Multi-Channel Notification System
Supports Telegram, console, and extensible notification channels
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional

try:
    from telegram import Bot
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False

from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, NOTIFICATION_CHANNELS

class NotificationManager:
    def __init__(self):
        """Initialize notification channels"""
        self.telegram_bot = None
        self.chat_id = TELEGRAM_CHAT_ID
        
        # Initialize Telegram if enabled and available
        if NOTIFICATION_CHANNELS.get('telegram', False) and TELEGRAM_AVAILABLE:
            self._init_telegram()
        elif NOTIFICATION_CHANNELS.get('telegram', False):
            logging.warning("Telegram notifications enabled but python-telegram-bot not installed")
    
    def _init_telegram(self):
        """Initialize Telegram bot"""
        try:
            if TELEGRAM_BOT_TOKEN and TELEGRAM_BOT_TOKEN != 'your-telegram-token-here':
                self.telegram_bot = Bot(token=TELEGRAM_BOT_TOKEN)
                logging.info("Telegram bot initialized successfully")
            else:
                logging.warning("Telegram bot token not configured")
        except Exception as e:
            logging.error(f"Failed to initialize Telegram bot: {str(e)}")
    
    def send_message(self, message: str, channels: List[str] = None) -> bool:
        """Send message through specified channels"""
        if channels is None:
            channels = [channel for channel, enabled in NOTIFICATION_CHANNELS.items() if enabled]
        
        success = False
        
        for channel in channels:
            try:
                if channel == 'telegram':
                    success |= self._send_telegram(message)
                elif channel == 'console':
                    success |= self._send_console(message)
                elif channel == 'email':
                    success |= self._send_email(message)
            except Exception as e:
                logging.error(f"Error sending message via {channel}: {str(e)}")
        
        return success
    
    def _send_telegram(self, message: str) -> bool:
        """Send message via Telegram"""
        try:
            if not self.telegram_bot or not self.chat_id or self.chat_id == 'your-chat-id-here':
                logging.info("Telegram not configured - would send: " + message[:50] + "...")
                return False
            
            # Split long messages
            max_length = 4096  # Telegram message limit
            if len(message) > max_length:
                chunks = [message[i:i+max_length] for i in range(0, len(message), max_length)]
                for chunk in chunks:
                    self.telegram_bot.send_message(chat_id=self.chat_id, text=chunk)
            else:
                self.telegram_bot.send_message(chat_id=self.chat_id, text=message)
            
            logging.info("Message sent via Telegram")
            return True
            
        except Exception as e:
            logging.error(f"Failed to send Telegram message: {str(e)}")
            return False
    
    def _send_console(self, message: str) -> bool:
        """Send message to console"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_message = f"\n{'='*50}\n[{timestamp}] NOTIFICATION\n{'='*50}\n{message}\n{'='*50}\n"
            print(formatted_message)
            logging.info("Message sent to console")
            return True
        except Exception as e:
            logging.error(f"Failed to send console message: {str(e)}")
            return False
    
    def _send_email(self, message: str) -> bool:
        """Send message via email (placeholder for future implementation)"""
        # TODO: Implement email notifications
        logging.info("Email notifications not yet implemented")
        return False
    
    def send_urgent_notification(self, message: str) -> bool:
        """Send urgent notification through all available channels"""
        urgent_message = f"🚨 URGENT NOTIFICATION 🚨\n\n{message}"
        return self.send_message(urgent_message)
    
    def send_daily_reminder(self, study_plan: str, assignments: List[Dict]) -> bool:
        """Send formatted daily reminder"""
        try:
            message = f"🌅 Good Morning! Here's your study plan for {datetime.now().strftime('%A, %B %d, %Y')}:\n\n"
            
            # Add urgent assignments
            urgent_assignments = [a for a in assignments if a.get('priority') == 'HIGH']
            if urgent_assignments:
                message += "🚨 URGENT DEADLINES:\n"
                for assignment in urgent_assignments:
                    message += f"• {assignment['title']} - Due: {assignment['due_date']}\n"
                message += "\n"
            
            # Add study plan
            message += f"📋 Today's Study Plan:\n{study_plan}\n\n"
            
            # Add motivational message
            message += "💪 You've got this! Remember to take breaks and stay hydrated. 🌟"
            
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending daily reminder: {str(e)}")
            return False
    
    def send_assignment_reminder(self, assignment: Dict, days_until_due: int) -> bool:
        """Send reminder for specific assignment"""
        try:
            if days_until_due <= 0:
                urgency = "🚨 DUE TODAY"
            elif days_until_due == 1:
                urgency = "⚠️ DUE TOMORROW"
            elif days_until_due <= 3:
                urgency = "📅 DUE SOON"
            else:
                urgency = "📋 UPCOMING"
            
            message = f"{urgency}\n\n"
            message += f"📚 Assignment: {assignment['title']}\n"
            message += f"📅 Due Date: {assignment['due_date']}\n"
            message += f"⏰ Days Remaining: {days_until_due}\n"
            message += f"🎯 Priority: {assignment['priority']}\n"
            
            if assignment.get('description'):
                message += f"📝 Description: {assignment['description']}\n"
            
            message += f"\n💡 Time to get started! Break it down into smaller tasks."
            
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending assignment reminder: {str(e)}")
            return False
    
    def send_progress_update(self, completed_tasks: List[str], remaining_tasks: List[str]) -> bool:
        """Send progress update notification"""
        try:
            total_tasks = len(completed_tasks) + len(remaining_tasks)
            completion_rate = len(completed_tasks) / total_tasks * 100 if total_tasks > 0 else 0
            
            message = f"📊 Progress Update - {datetime.now().strftime('%B %d, %Y')}\n\n"
            message += f"✅ Completed: {len(completed_tasks)} tasks\n"
            message += f"📋 Remaining: {len(remaining_tasks)} tasks\n"
            message += f"📈 Progress: {completion_rate:.1f}%\n\n"
            
            if completed_tasks:
                message += "🎉 Completed Today:\n"
                for task in completed_tasks[-3:]:  # Show last 3 completed
                    message += f"• {task}\n"
                message += "\n"
            
            if remaining_tasks:
                message += "🎯 Next Up:\n"
                for task in remaining_tasks[:3]:  # Show next 3 tasks
                    message += f"• {task}\n"
                message += "\n"
            
            if completion_rate >= 80:
                message += "🌟 Excellent progress! Keep up the great work!"
            elif completion_rate >= 50:
                message += "👍 Good progress! You're on track!"
            else:
                message += "💪 Let's pick up the pace! You can do this!"
            
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending progress update: {str(e)}")
            return False
    
    def send_study_session_reminder(self, subject: str, duration: int = 45) -> bool:
        """Send study session start reminder"""
        try:
            message = f"📚 Study Session Starting!\n\n"
            message += f"Subject: {subject}\n"
            message += f"Duration: {duration} minutes\n"
            message += f"Start Time: {datetime.now().strftime('%H:%M')}\n\n"
            message += "🎯 Tips for this session:\n"
            message += "• Remove distractions\n"
            message += "• Take notes actively\n"
            message += "• Set a timer\n"
            message += "• Stay focused!\n\n"
            message += "Good luck! 🍀"
            
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending study session reminder: {str(e)}")
            return False
    
    def send_break_reminder(self) -> bool:
        """Send break time reminder"""
        try:
            message = "☕ Break Time!\n\n"
            message += "You've been studying hard. Time for a well-deserved break!\n\n"
            message += "💡 Break suggestions:\n"
            message += "• Stretch or walk around\n"
            message += "• Hydrate with water\n"
            message += "• Get some fresh air\n"
            message += "• Rest your eyes\n\n"
            message += "⏰ Take 10-15 minutes, then get back to it!"
            
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending break reminder: {str(e)}")
            return False
    
    def send_weekly_summary(self, summary_data: Dict) -> bool:
        """Send weekly summary report"""
        try:
            message = f"📊 Weekly Summary - Week of {datetime.now().strftime('%B %d, %Y')}\n\n"
            
            if 'completed_assignments' in summary_data:
                message += f"✅ Assignments Completed: {summary_data['completed_assignments']}\n"
            
            if 'study_hours' in summary_data:
                message += f"⏰ Total Study Hours: {summary_data['study_hours']}\n"
            
            if 'subjects_covered' in summary_data:
                message += f"📚 Subjects Covered: {', '.join(summary_data['subjects_covered'])}\n"
            
            message += "\n🎯 Next Week's Focus:\n"
            if 'upcoming_deadlines' in summary_data:
                for deadline in summary_data['upcoming_deadlines'][:3]:
                    message += f"• {deadline}\n"
            
            message += "\n🌟 Keep up the excellent work!"
            
            return self.send_message(message)
            
        except Exception as e:
            logging.error(f"Error sending weekly summary: {str(e)}")
            return False
