"""
Enhanced Calendar Management Module
Integrates with Google Calendar API for assignment tracking
"""

from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

try:
    from googleapiclient.discovery import build
    from google.oauth2 import service_account
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False

from config import GOOGLE_CREDENTIALS_PATH

class CalendarManager:
    def __init__(self):
        """Initialize Google Calendar connection"""
        self.service = None
        if GOOGLE_AVAILABLE:
            self.scopes = ['https://www.googleapis.com/auth/calendar.readonly']
            self.service = self._authenticate()
        else:
            logging.warning("Google Calendar API not available. Install google-api-python-client to enable.")
        
    def _authenticate(self):
        """Authenticate with Google Calendar API"""
        try:
            import os
            if not os.path.exists(GOOGLE_CREDENTIALS_PATH):
                logging.warning(f"Google credentials file not found: {GOOGLE_CREDENTIALS_PATH}")
                return None
                
            creds = service_account.Credentials.from_service_account_file(
                GOOGLE_CREDENTIALS_PATH, scopes=self.scopes
            )
            service = build("calendar", "v3", credentials=creds)
            logging.info("Google Calendar authentication successful")
            return service
        except Exception as e:
            logging.error(f"Google Calendar authentication failed: {str(e)}")
            return None
    
    def get_upcoming_assignments(self, days_ahead: int = 7) -> List[Dict]:
        """Get assignments due in the next specified days"""
        if not self.service:
            # Return sample data for demo purposes
            return self._get_sample_assignments()
        
        try:
            # Calculate time range
            now = datetime.utcnow().isoformat() + 'Z'
            end_time = (datetime.utcnow() + timedelta(days=days_ahead)).isoformat() + 'Z'
            
            # Fetch events
            events_result = self.service.events().list(
                calendarId='primary',
                timeMin=now,
                timeMax=end_time,
                maxResults=50,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            assignments = []
            
            for event in events:
                # Filter for assignment-related events
                if self._is_assignment(event):
                    assignment = self._parse_assignment(event)
                    if assignment:
                        assignments.append(assignment)
            
            logging.info(f"Retrieved {len(assignments)} upcoming assignments")
            return assignments
            
        except Exception as e:
            logging.error(f"Error fetching assignments: {str(e)}")
            return self._get_sample_assignments()
    
    def _get_sample_assignments(self) -> List[Dict]:
        """Return sample assignments for demo purposes"""
        sample_assignments = [
            {
                'id': 'sample_1',
                'title': 'Financial Statement Analysis Project',
                'description': 'Analyze Apple Inc. financial statements for the past 3 years',
                'due_date': (datetime.now() + timedelta(days=3)).isoformat(),
                'due_datetime': datetime.now() + timedelta(days=3),
                'days_until_due': 3,
                'priority': 'HIGH',
                'subject': 'Accounting',
                'calendar_link': ''
            },
            {
                'id': 'sample_2',
                'title': 'Investment Portfolio Report',
                'description': 'Create a diversified investment portfolio with risk analysis',
                'due_date': (datetime.now() + timedelta(days=7)).isoformat(),
                'due_datetime': datetime.now() + timedelta(days=7),
                'days_until_due': 7,
                'priority': 'MEDIUM',
                'subject': 'Finance',
                'calendar_link': ''
            }
        ]
        return sample_assignments
    
    def get_weekly_assignments(self) -> List[Dict]:
        """Get all assignments for the current week"""
        return self.get_upcoming_assignments(days_ahead=7)
    
    def get_assignments_by_subject(self, subject: str) -> List[Dict]:
        """Get assignments filtered by subject"""
        all_assignments = self.get_upcoming_assignments(days_ahead=30)
        subject_assignments = []
        
        for assignment in all_assignments:
            if subject.lower() in assignment.get('title', '').lower() or \
               subject.lower() in assignment.get('description', '').lower():
                subject_assignments.append(assignment)
        
        return subject_assignments
    
    def _is_assignment(self, event: Dict) -> bool:
        """Check if an event is an assignment"""
        title = event.get('summary', '').lower()
        description = event.get('description', '').lower()
        
        # Keywords that indicate an assignment
        assignment_keywords = [
            'assignment', 'project', 'homework', 'essay', 'report',
            'exam', 'test', 'quiz', 'presentation', 'due', 'submit'
        ]
        
        return any(keyword in title or keyword in description 
                  for keyword in assignment_keywords)
    
    def _parse_assignment(self, event: Dict) -> Optional[Dict]:
        """Parse event data into assignment format"""
        try:
            title = event.get('summary', 'Untitled Assignment')
            description = event.get('description', '')
            
            # Get due date
            start = event.get('start', {})
            due_date = start.get('dateTime', start.get('date'))
            
            if due_date:
                # Parse due date
                if 'T' in due_date:
                    due_datetime = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
                else:
                    due_datetime = datetime.fromisoformat(due_date)
                
                # Calculate days until due
                days_until_due = (due_datetime.date() - datetime.now().date()).days
                
                # Determine priority based on due date
                if days_until_due <= 1:
                    priority = "HIGH"
                elif days_until_due <= 3:
                    priority = "MEDIUM"
                else:
                    priority = "LOW"
                
                # Extract subject from title or description
                subject = self._extract_subject(title, description)
                
                return {
                    'id': event.get('id'),
                    'title': title,
                    'description': description,
                    'due_date': due_date,
                    'due_datetime': due_datetime,
                    'days_until_due': days_until_due,
                    'priority': priority,
                    'subject': subject,
                    'calendar_link': event.get('htmlLink', '')
                }
        
        except Exception as e:
            logging.error(f"Error parsing assignment: {str(e)}")
            return None
    
    def _extract_subject(self, title: str, description: str) -> str:
        """Extract subject from title or description"""
        from config import STUDY_CATEGORIES
        
        text = (title + ' ' + description).lower()
        
        for category in STUDY_CATEGORIES:
            if category.lower() in text:
                return category
        
        return "Other"
    
    def add_study_session(self, title: str, start_time: datetime, duration_minutes: int = 45):
        """Add a study session to the calendar"""
        if not self.service:
            logging.info(f"Would add study session: {title} (Google Calendar not available)")
            return False
        
        try:
            end_time = start_time + timedelta(minutes=duration_minutes)
            
            event = {
                'summary': f'Study Session: {title}',
                'description': 'Automated study session created by AI Assistant',
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'UTC',
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'UTC',
                },
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'popup', 'minutes': 10},
                    ],
                },
            }
            
            event = self.service.events().insert(calendarId='primary', body=event).execute()
            logging.info(f"Study session added: {title}")
            return True
            
        except Exception as e:
            logging.error(f"Error adding study session: {str(e)}")
            return False
