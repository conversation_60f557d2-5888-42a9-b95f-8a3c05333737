import chromadb

chroma_client = chromadb.Client()
collection = chroma_client.create_collection(name="notes")

# Add some notes
collection.add(
    documents=["Accounting systems help in ...", "AIS integrates IT and finance ..."],
    ids=["note1", "note2"]
)

# Query
results = collection.query(
    query_texts=["Summarize accounting information systems basics"],
    n_results=2
)
print(results["documents"])
