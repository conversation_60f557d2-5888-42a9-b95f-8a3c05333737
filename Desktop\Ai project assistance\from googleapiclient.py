from googleapiclient.discovery import build
from google.oauth2 import service_account

# Connect to Google Calendar API
SCOPES = ['https://www.googleapis.com/auth/calendar.readonly']
creds = service_account.Credentials.from_service_account_file(
    "credentials.json", scopes=SCOPES
)

service = build("calendar", "v3", credentials=creds)

def get_assignments():
    events_result = service.events().list(
        calendarId='primary',
        maxResults=10,
        singleEvents=True,
        orderBy='startTime'
    ).execute()
    
    events = events_result.get('items', [])
    deadlines = []
    for event in events:
        deadlines.append({
            "title": event['summary'],
            "due_date": event['start'].get('dateTime', event['start'].get('date'))
        })
    return deadlines

print(get_assignments())
